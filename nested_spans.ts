#!/usr/bin/env node
/**
 * TypeScript script to create a log with deeply nested spans (child spans that have their own child spans).
 *
 * This demonstrates multi-level hierarchical logging:
 * - Level 1: Main task span
 * - Level 2: Major sub-tasks
 * - Level 3: Detailed steps within sub-tasks
 * - Level 4: Individual operations within steps
 *
 * Usage:
 *     npx tsx nested_spans.ts
 *     # or compile and run:
 *     tsc nested_spans.ts && node nested_spans.js
 *
 * Make sure to set your BRAINTRUST_API_KEY environment variable.
 */

import * as braintrust from "braintrust";

async function main() {
  // Initialize logger for a project
  const logger = braintrust.initLogger({ projectName: "pedro-repro4667" });

  // Level 1: Main task span
  await logger.traced(async (mainSpan) => {
    mainSpan.log({
      input: "Analyze the quarterly financial report document",
      metadata: {
        document_id: "doc_q4_2023_financial",
        document_type: "financial_report",
        pages: 45,
      },
    });

    // Level 2: Document preprocessing
    await mainSpan.traced(async (preprocessSpan) => {
      preprocessSpan.log({
        input: "Raw PDF document with 45 pages",
        metadata: { stage: "preprocessing" },
      });

      // Level 3: Text extraction from preprocessing
      await preprocessSpan.traced(async (extractSpan) => {
        extractSpan.log({
          input: "PDF file with mixed content",
          metadata: { extraction_method: "OCR + text_layer" },
        });

        // Level 4: Individual page processing
        await extractSpan.traced(async (page1Span) => {
          page1Span.log({
            input: "Page 1: Executive Summary",
            output: "Extracted 1,247 characters",
            scores: { text_quality: 0.98, confidence: 0.95 },
          });
        }, { name: "page_1_extraction" });

        await extractSpan.traced(async (page2Span) => {
          page2Span.log({
            input: "Page 2: Financial Overview",
            output: "Extracted 1,456 characters",
            scores: { text_quality: 0.94, confidence: 0.92 },
          });
        }, { name: "page_2_extraction" });

        // Log extraction summary
        extractSpan.log({
          output: "Successfully extracted text from 45 pages",
          scores: { overall_quality: 0.96, pages_processed: 0.45 },
        });
      }, { name: "text_extraction" });

      // Level 3: Text cleaning from preprocessing
      await preprocessSpan.traced(async (cleanSpan) => {
        cleanSpan.log({
          input: "Raw extracted text with formatting artifacts",
          metadata: {
            cleaning_steps: ["remove_headers", "fix_spacing", "normalize_encoding"],
          },
        });

        // Level 4: Individual cleaning steps
        await cleanSpan.traced(async (headersSpan) => {
          headersSpan.log({
            input: "Text with page headers and footers",
            output: "Clean text without headers/footers",
            scores: { artifacts_removed: 0.99 },
          });
        }, { name: "remove_headers_footers" });

        await cleanSpan.traced(async (whitespaceSpan) => {
          whitespaceSpan.log({
            input: "Text with irregular spacing",
            output: "Text with normalized spacing",
            scores: { formatting_quality: 0.97 },
          });
        }, { name: "normalize_whitespace" });

        cleanSpan.log({
          output: "Cleaned and normalized text ready for analysis",
          scores: { cleaning_quality: 0.95 },
        });
      }, { name: "text_cleaning" });

      preprocessSpan.log({
        output: "Document preprocessing completed",
        scores: { preprocessing_success: 1.0 },
      });
    }, { name: "document_preprocessing" });

    // Level 2: Content analysis
    await mainSpan.traced(async (analysisSpan) => {
      analysisSpan.log({
        input: "Cleaned document text",
        metadata: { analysis_type: "financial_metrics_extraction" },
      });

      // Level 3: Financial metrics extraction
      await analysisSpan.traced(async (metricsSpan) => {
        metricsSpan.log({
          input: "Financial report text",
          metadata: {
            target_metrics: ["revenue", "profit", "expenses", "growth_rate"],
          },
        });

        // Level 4: Individual metric extraction
        await metricsSpan.traced(async (revenueSpan) => {
          revenueSpan.log({
            input: "Text sections mentioning revenue",
            output: { q4_revenue: "$2.4M", yoy_growth: "15%" },
            scores: { extraction_confidence: 0.94 },
          });
        }, { name: "revenue_extraction" });

        await metricsSpan.traced(async (profitSpan) => {
          profitSpan.log({
            input: "Text sections mentioning profit/loss",
            output: { net_profit: "$340K", profit_margin: "14.2%" },
            scores: { extraction_confidence: 0.91 },
          });
        }, { name: "profit_extraction" });

        metricsSpan.log({
          output: "Successfully extracted key financial metrics",
          scores: { metrics_completeness: 0.88 },
        });
      }, { name: "financial_metrics_extraction" });

      // Level 3: Sentiment analysis
      await analysisSpan.traced(async (sentimentSpan) => {
        sentimentSpan.log({
          input: "Financial report narrative sections",
          metadata: {
            analysis_sections: ["executive_summary", "outlook", "risks"],
          },
        });

        // Level 4: Section-specific sentiment
        await sentimentSpan.traced(async (execSentimentSpan) => {
          execSentimentSpan.log({
            input: "Executive summary text",
            output: { sentiment: "positive", confidence: 0.82 },
            scores: { positivity: 0.7 },
          });
        }, { name: "executive_summary_sentiment" });

        await sentimentSpan.traced(async (outlookSentimentSpan) => {
          outlookSentimentSpan.log({
            input: "Future outlook section",
            output: { sentiment: "cautiously_optimistic", confidence: 0.76 },
            scores: { positivity: 0.6 },
          });
        }, { name: "outlook_sentiment" });

        sentimentSpan.log({
          output: "Overall document sentiment: positive with caution",
          scores: { overall_sentiment: 0.65 },
        });
      }, { name: "sentiment_analysis" });

      analysisSpan.log({
        output: "Content analysis completed",
        scores: { analysis_quality: 0.89 },
      });
    }, { name: "content_analysis" });

    // Level 2: Report generation
    await mainSpan.traced(async (reportSpan) => {
      reportSpan.log({
        input: "Extracted metrics and sentiment analysis",
        metadata: { report_format: "executive_summary" },
      });

      // Level 3: Summary creation
      await reportSpan.traced(async (summarySpan) => {
        summarySpan.log({
          input: "Financial metrics and sentiment data",
          output:
            "Q4 2023 showed strong performance with $2.4M revenue (15% YoY growth) and healthy 14.2% profit margin. Overall sentiment is positive with cautious optimism for future quarters.",
          scores: { summary_quality: 0.92, conciseness: 0.88 },
        });
      }, { name: "summary_creation" });

      reportSpan.log({
        output: "Executive summary report generated",
        scores: { report_completeness: 0.94 },
      });
    }, { name: "report_generation" });

    // Log final result on main span
    mainSpan.log({
      output: "Document analysis pipeline completed successfully",
      expected:
        "Comprehensive financial document analysis with extracted metrics and insights",
      scores: {
        pipeline_success: 1.0,
        overall_quality: 0.91,
        processing_efficiency: 0.87,
      },
      metadata: {
        total_spans_created: 15,
        max_nesting_depth: 4,
        pipeline_completed: true,
      },
      tags: ["document_analysis", "financial_report", "multi_level_processing"],
    });
  }, { name: "document_analysis_pipeline" });

  console.log("Created deeply nested trace structure:");
  console.log("Level 1: document_analysis_pipeline (main)");
  console.log("├── Level 2: document_preprocessing");
  console.log("│   ├── Level 3: text_extraction");
  console.log("│   │   ├── Level 4: page_1_extraction");
  console.log("│   │   └── Level 4: page_2_extraction");
  console.log("│   └── Level 3: text_cleaning");
  console.log("│       ├── Level 4: remove_headers_footers");
  console.log("│       └── Level 4: normalize_whitespace");
  console.log("├── Level 2: content_analysis");
  console.log("│   ├── Level 3: financial_metrics_extraction");
  console.log("│   │   ├── Level 4: revenue_extraction");
  console.log("│   │   └── Level 4: profit_extraction");
  console.log("│   └── Level 3: sentiment_analysis");
  console.log("│       ├── Level 4: executive_summary_sentiment");
  console.log("│       └── Level 4: outlook_sentiment");
  console.log("└── Level 2: report_generation");
  console.log("    └── Level 3: summary_creation");

  // Flush to ensure all logs are sent to the server
  await logger.flush();
  console.log("\nAll nested spans have been sent to Braintrust server");
  console.log("Check your Braintrust dashboard to explore the hierarchical trace structure");
}

if (require.main === module) {
  main().catch(console.error);
}
